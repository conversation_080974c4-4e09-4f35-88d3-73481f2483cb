/**
 * Self-Assessment Questionnaire Integration Tests
 * Tests the complete assessment flow from start to completion
 */

import { TestSetup, setupNextJSMocks, createMockSession, testData } from '../utils/testSetup';

// Setup mocks
setupNextJSMocks();

jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn()
}));

const { getServerSession } = require('next-auth/next');

describe('Self-Assessment Questionnaire Flow', () => {
  let testSetup: TestSetup;
  let testUser: any;
  let mockSession: any;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setupTestDatabase();
  });

  beforeEach(async () => {
    const { testUser: user } = await testSetup.createTestData();
    testUser = user;
    mockSession = createMockSession(testUser.id, testUser.email);
    getServerSession.mockResolvedValue(mockSession);
  });

  afterAll(async () => {
    await testSetup.cleanupTestData();
    await testSetup.disconnect();
  });

  describe('Assessment Initialization', () => {
    it('should create new assessment for first-time user', async () => {
      // Test new assessment creation
      const newAssessment = {
        userId: testUser.id,
        currentStep: 0,
        formData: {},
        status: 'IN_PROGRESS',
        createdAt: new Date()
      };

      expect(newAssessment.userId).toBe(testUser.id);
      expect(newAssessment.currentStep).toBe(0);
      expect(newAssessment.status).toBe('IN_PROGRESS');
      expect(Object.keys(newAssessment.formData)).toHaveLength(0);
    });

    it('should resume existing assessment', async () => {
      // Test resuming existing assessment
      const existingAssessment = {
        userId: testUser.id,
        currentStep: 3,
        formData: {
          dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance'],
          desired_outcomes_skill_a: 'high',
          work_environment_preference: 'hybrid'
        },
        status: 'IN_PROGRESS'
      };

      expect(existingAssessment.currentStep).toBe(3);
      expect(existingAssessment.formData.dissatisfaction_triggers).toHaveLength(2);
      expect(existingAssessment.status).toBe('IN_PROGRESS');
    });

    it('should validate assessment ownership', async () => {
      const assessmentUserId = testUser.id;
      const sessionUserId = mockSession.user.id;

      expect(assessmentUserId).toBe(sessionUserId);
    });
  });

  describe('Assessment Progress Tracking', () => {
    it('should save progress at each step', async () => {
      const progressSteps = [
        {
          step: 1,
          data: { dissatisfaction_triggers: ['lack_of_growth'] }
        },
        {
          step: 2,
          data: { desired_outcomes_skill_a: 'high', desired_outcomes_skill_b: 'medium' }
        },
        {
          step: 3,
          data: { work_environment_preference: 'remote', team_size_preference: 'small' }
        }
      ];

      progressSteps.forEach((step, index) => {
        expect(step.step).toBe(index + 1);
        expect(step.data).toBeDefined();
        expect(Object.keys(step.data)).toHaveLength(step.step === 2 ? 2 : 1);
      });
    });

    it('should validate step progression', async () => {
      const validProgression = [0, 1, 2, 3, 4, 5];
      const invalidJump = [0, 1, 4]; // Skipping steps 2 and 3

      validProgression.forEach((step, index) => {
        if (index > 0) {
          expect(step).toBe(validProgression[index - 1] + 1);
        }
      });

      // Test invalid progression
      expect(invalidJump[2] - invalidJump[1]).toBeGreaterThan(1);
    });

    it('should handle step navigation (back/forward)', async () => {
      const navigationTest = {
        currentStep: 3,
        goBack: () => 2,
        goForward: () => 4,
        goToStep: (step: number) => step
      };

      expect(navigationTest.goBack()).toBe(2);
      expect(navigationTest.goForward()).toBe(4);
      expect(navigationTest.goToStep(1)).toBe(1);
    });
  });

  describe('Assessment Data Validation', () => {
    it('should validate required fields per step', async () => {
      const stepValidations = {
        step1: {
          required: ['dissatisfaction_triggers'],
          data: { dissatisfaction_triggers: ['lack_of_growth'] },
          isValid: true
        },
        step2: {
          required: ['desired_outcomes_skill_a'],
          data: { desired_outcomes_skill_a: 'high' },
          isValid: true
        },
        step3: {
          required: ['work_environment_preference'],
          data: { work_environment_preference: 'remote' },
          isValid: true
        }
      };

      Object.values(stepValidations).forEach(validation => {
        validation.required.forEach(field => {
          expect(validation.data).toHaveProperty(field);
        });
        expect(validation.isValid).toBe(true);
      });
    });

    it('should validate answer formats and values', async () => {
      const validAnswers = {
        dissatisfaction_triggers: ['lack_of_growth', 'poor_compensation'],
        desired_outcomes_skill_a: 'high',
        work_environment_preference: 'remote',
        team_size_preference: 'small',
        leadership_interest: 'yes'
      };

      const invalidAnswers = {
        dissatisfaction_triggers: 'should_be_array',
        desired_outcomes_skill_a: 'invalid_value',
        work_environment_preference: null,
        team_size_preference: 123,
        leadership_interest: 'maybe'
      };

      // Test valid answers
      expect(Array.isArray(validAnswers.dissatisfaction_triggers)).toBe(true);
      expect(['low', 'medium', 'high']).toContain(validAnswers.desired_outcomes_skill_a);
      expect(['remote', 'hybrid', 'office']).toContain(validAnswers.work_environment_preference);

      // Test invalid answers
      expect(Array.isArray(invalidAnswers.dissatisfaction_triggers)).toBe(false);
      expect(['low', 'medium', 'high']).not.toContain(invalidAnswers.desired_outcomes_skill_a);
      expect(invalidAnswers.work_environment_preference).toBeNull();
    });

    it('should sanitize user inputs', async () => {
      const maliciousInputs = {
        custom_text: '<script>alert("xss")</script>',
        other_field: 'javascript:alert("xss")',
        comment: '<img src=x onerror=alert("xss")>'
      };

      // Test that malicious inputs are detected
      Object.values(maliciousInputs).forEach(input => {
        expect(input).toMatch(/<|javascript:|onerror=/);
        // In real implementation, these should be sanitized
      });
    });
  });

  describe('Assessment Completion', () => {
    it('should complete assessment with all required data', async () => {
      const completeAssessment = {
        dissatisfaction_triggers: ['lack_of_growth', 'poor_work_life_balance'],
        desired_outcomes_skill_a: 'high',
        desired_outcomes_skill_b: 'medium',
        work_environment_preference: 'hybrid',
        team_size_preference: 'medium',
        leadership_interest: 'yes',
        industry_preference: ['technology', 'healthcare'],
        salary_importance: 'high',
        work_life_balance_importance: 'high',
        learning_style: 'hands_on'
      };

      // Validate completion criteria
      const requiredFields = [
        'dissatisfaction_triggers',
        'desired_outcomes_skill_a',
        'work_environment_preference',
        'leadership_interest'
      ];

      requiredFields.forEach(field => {
        expect(completeAssessment).toHaveProperty(field);
        expect(completeAssessment[field as keyof typeof completeAssessment]).toBeDefined();
      });
    });

    it('should generate assessment results and recommendations', async () => {
      const assessmentResults = {
        personalityType: 'ANALYTICAL_LEADER',
        careerMatches: [
          { title: 'Software Engineering Manager', match: 92 },
          { title: 'Product Manager', match: 87 },
          { title: 'Technical Lead', match: 85 }
        ],
        skillGaps: [
          { skill: 'Leadership', currentLevel: 'beginner', targetLevel: 'intermediate' },
          { skill: 'Project Management', currentLevel: 'none', targetLevel: 'beginner' }
        ],
        recommendedResources: [
          { id: 'resource-1', title: 'Leadership Fundamentals', relevance: 'high' },
          { id: 'resource-2', title: 'Agile Project Management', relevance: 'medium' }
        ]
      };

      // Validate results structure
      expect(assessmentResults.personalityType).toBeDefined();
      expect(assessmentResults.careerMatches).toHaveLength(3);
      expect(assessmentResults.skillGaps).toHaveLength(2);
      expect(assessmentResults.recommendedResources).toHaveLength(2);

      // Validate career matches are sorted by match percentage
      const matches = assessmentResults.careerMatches;
      for (let i = 1; i < matches.length; i++) {
        expect(matches[i - 1].match).toBeGreaterThanOrEqual(matches[i].match);
      }
    });

    it('should save completed assessment to database', async () => {
      const completedAssessment = {
        userId: testUser.id,
        status: 'COMPLETED',
        completedAt: new Date(),
        results: {
          personalityType: 'ANALYTICAL_LEADER',
          careerMatches: [],
          skillGaps: [],
          recommendedResources: []
        }
      };

      expect(completedAssessment.status).toBe('COMPLETED');
      expect(completedAssessment.completedAt).toBeInstanceOf(Date);
      expect(completedAssessment.results).toBeDefined();
    });
  });

  describe('Assessment Edge Cases', () => {
    it('should handle incomplete submissions', async () => {
      const incompleteData = {
        dissatisfaction_triggers: ['lack_of_growth'],
        // Missing required fields
      };

      const requiredFields = ['desired_outcomes_skill_a', 'work_environment_preference'];
      const missingFields = requiredFields.filter(field => 
        !incompleteData.hasOwnProperty(field)
      );

      expect(missingFields).toHaveLength(2);
    });

    it('should handle assessment timeout/expiration', async () => {
      const assessmentAge = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days old
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

      const isExpired = (Date.now() - assessmentAge) > maxAge;
      expect(isExpired).toBe(true);
    });

    it('should handle concurrent assessment updates', async () => {
      const concurrentUpdates = [
        { step: 2, timestamp: Date.now() },
        { step: 2, timestamp: Date.now() + 100 },
        { step: 3, timestamp: Date.now() + 200 }
      ];

      // Last update should win
      const latestUpdate = concurrentUpdates.reduce((latest, current) => 
        current.timestamp > latest.timestamp ? current : latest
      );

      expect(latestUpdate.step).toBe(3);
    });

    it('should validate assessment data integrity', async () => {
      const assessmentData = {
        userId: testUser.id,
        formData: {
          dissatisfaction_triggers: ['lack_of_growth'],
          desired_outcomes_skill_a: 'high'
        }
      };

      // Check data integrity
      expect(assessmentData.userId).toBe(testUser.id);
      expect(typeof assessmentData.formData).toBe('object');
      expect(assessmentData.formData).not.toBeNull();
    });
  });
});

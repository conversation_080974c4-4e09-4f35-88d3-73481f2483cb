import { PrismaClient } from '@prisma/client';

// Global test setup
export class TestSetup {
  private static instance: TestSetup;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'file:./test.db'
        }
      }
    });
  }

  static getInstance(): TestSetup {
    if (!TestSetup.instance) {
      TestSetup.instance = new TestSetup();
    }
    return TestSetup.instance;
  }

  async setupTestDatabase() {
    // Clean up existing test data
    await this.cleanupTestData();
    
    // Create test data if needed
    await this.createTestData();
  }

  async cleanupTestData() {
    // Delete test data in correct order to avoid foreign key constraints
    const tables = [
      'UserProgress',
      'ResourceRating',
      'ForumPost',
      'Assessment',
      'User',
      'LearningResource',
      'CareerPath'
    ];

    for (const table of tables) {
      try {
        await this.prisma.$executeRawUnsafe(`DELETE FROM "${table}" WHERE id LIKE 'test-%'`);
      } catch (error) {
        // Table might not exist or be empty, continue
        console.warn(`Could not clean table ${table}:`, error);
      }
    }
  }

  async createTestData() {
    // Create test user
    const testUser = await this.prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        id: 'test-user-1',
        email: '<EMAIL>',
        name: 'Test User',
        hashedPassword: '$2b$10$test.hash.for.testing.purposes.only',
        emailVerified: new Date(),
      }
    });

    // Create test learning resources
    await this.prisma.learningResource.upsert({
      where: { id: 'test-resource-1' },
      update: {},
      create: {
        id: 'test-resource-1',
        title: 'Test Resource 1',
        description: 'A test learning resource',
        url: 'https://example.com/resource1',
        type: 'COURSE',
        difficulty: 'BEGINNER',
        estimatedHours: 10,
        category: 'Technology',
        tags: ['test', 'programming'],
        isActive: true,
      }
    });

    // Create test career path
    await this.prisma.careerPath.upsert({
      where: { id: 'test-career-1' },
      update: {},
      create: {
        id: 'test-career-1',
        title: 'Test Career Path',
        description: 'A test career path',
        category: 'Technology',
        difficulty: 'INTERMEDIATE',
        estimatedDuration: '6 months',
        skills: ['JavaScript', 'React', 'Node.js'],
        isActive: true,
      }
    });

    return { testUser };
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }

  getPrisma() {
    return this.prisma;
  }
}

// Mock Next.js server environment
export function setupNextJSMocks() {
  // Mock NextRequest and NextResponse
  global.Request = global.Request || class MockRequest {
    constructor(public url: string, public init?: RequestInit) {}
    json() { return Promise.resolve(this.init?.body ? JSON.parse(this.init.body as string) : {}); }
  };

  global.Response = global.Response || class MockResponse {
    constructor(public body?: any, public init?: ResponseInit) {}
    json() { return Promise.resolve(this.body); }
    static json(data: any, init?: ResponseInit) {
      return new MockResponse(data, init);
    }
  };

  // Mock environment variables
  process.env.NODE_ENV = 'test';
  process.env.NEXTAUTH_SECRET = 'test-secret';
  process.env.NEXTAUTH_URL = 'http://localhost:3000';
  process.env.DATABASE_URL = 'file:./test.db';
}

// Mock authentication
export function createMockSession(userId: string, email: string) {
  return {
    user: {
      id: userId,
      email: email,
      name: 'Test User',
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };
}

// Test data generators
export const testData = {
  validUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User'
  },
  
  validAssessment: {
    currentStep: 1,
    formData: {
      dissatisfaction_triggers: ['lack_of_growth'],
      desired_outcomes_skill_a: 'high',
      work_environment_preference: 'remote'
    },
    status: 'IN_PROGRESS'
  },

  validLearningResource: {
    title: 'Test Learning Resource',
    description: 'A comprehensive test resource',
    url: 'https://example.com/resource',
    type: 'COURSE',
    difficulty: 'BEGINNER',
    estimatedHours: 10,
    category: 'Technology',
    tags: ['test', 'learning']
  },

  validForumPost: {
    title: 'Test Forum Post',
    content: 'This is a test forum post content',
    category: 'GENERAL'
  }
};

// Security test inputs
export const securityTestInputs = {
  xssAttempts: [
    '<script>alert("xss")</script>',
    'javascript:alert("xss")',
    '<img src=x onerror=alert("xss")>',
    '<svg onload=alert("xss")>',
  ],
  
  sqlInjectionAttempts: [
    "'; DROP TABLE users; --",
    "1' OR '1'='1",
    "admin'--",
    "' UNION SELECT * FROM users --",
  ],
  
  pathTraversalAttempts: [
    '../../../etc/passwd',
    '..\\..\\..\\windows\\system32\\config\\sam',
    '....//....//....//etc/passwd',
  ],
  
  oversizedInput: 'A'.repeat(10000),
  
  specialCharacters: '!@#$%^&*()_+-=[]{}|;\':",./<>?`~'
};

// Performance testing utilities
export async function measurePerformance<T>(
  operation: () => Promise<T>,
  maxExecutionTime: number = 5000
): Promise<{ result: T; executionTime: number; withinLimit: boolean }> {
  const startTime = performance.now();
  const result = await operation();
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  
  return {
    result,
    executionTime,
    withinLimit: executionTime <= maxExecutionTime
  };
}

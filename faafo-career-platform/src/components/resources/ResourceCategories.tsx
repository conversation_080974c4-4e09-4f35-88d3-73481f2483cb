'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Shield, BarChart, Blocks, Code, Megaphone, TrendingUp, Globe, Cpu, BookOpen, Video, Headphones, Award, Clock, Star, Users } from 'lucide-react';

interface CategoryStats {
  category: string;
  count: number;
  averageRating: number;
  totalRatings: number;
  skillLevels: {
    BEGINNER: number;
    INTERMEDIATE: number;
    ADVANCED: number;
  };
  types: {
    [key: string]: number;
  };
}

const categoryInfo = {
  CYBERSECURITY: {
    title: 'Cybersecurity',
    description: 'Protect digital assets and learn ethical hacking',
    icon: Shield,
    color: 'red',
    gradient: 'from-red-500 to-red-600',
  },
  DATA_SCIENCE: {
    title: 'Data Science',
    description: 'Analyze data and build machine learning models',
    icon: BarChart,
    color: 'gray',
    gradient: 'from-gray-500 to-gray-600',
  },
  BLOCKCHAIN: {
    title: 'Blockchain',
    description: 'Decentralized technologies and cryptocurrency',
    icon: Blocks,
    color: 'yellow',
    gradient: 'from-yellow-500 to-yellow-600',
  },
  WEB_DEVELOPMENT: {
    title: 'Web Development',
    description: 'Build modern web applications and websites',
    icon: Code,
    color: 'green',
    gradient: 'from-green-500 to-green-600',
  },
  DIGITAL_MARKETING: {
    title: 'Digital Marketing',
    description: 'Online marketing strategies and social media',
    icon: Megaphone,
    color: 'pink',
    gradient: 'from-pink-500 to-pink-600',
  },
  PROJECT_MANAGEMENT: {
    title: 'Project Management',
    description: 'Lead teams and manage complex projects',
    icon: TrendingUp,
    color: 'purple',
    gradient: 'from-purple-500 to-purple-600',
  },
  FINANCIAL_LITERACY: {
    title: 'Financial Literacy',
    description: 'Personal finance and investment strategies',
    icon: TrendingUp,
    color: 'emerald',
    gradient: 'from-emerald-500 to-emerald-600',
  },
  ARTIFICIAL_INTELLIGENCE: {
    title: 'Artificial Intelligence',
    description: 'AI, machine learning, and neural networks',
    icon: Cpu,
    color: 'indigo',
    gradient: 'from-indigo-500 to-indigo-600',
  },
  ENTREPRENEURSHIP: {
    title: 'Entrepreneurship',
    description: 'Start and grow your own business',
    icon: TrendingUp,
    color: 'orange',
    gradient: 'from-orange-500 to-orange-600',
  },
};

const getTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'course':
      return BookOpen;
    case 'certification':
      return Award;
    default:
      return BookOpen;
  }
};

export default function ResourceCategories() {
  const [categoryStats, setCategoryStats] = useState<CategoryStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategoryStats();
  }, []);

  const fetchCategoryStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/learning-resources/categories');
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategoryStats(result.data);
        } else {
          throw new Error(result.error || 'Failed to fetch category stats');
        }
      } else {
        throw new Error('Failed to fetch category stats');
      }
    } catch (error) {
      console.error('Error fetching category stats:', error);
      setError(error instanceof Error ? error.message : 'Failed to load category stats');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md animate-pulse">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <button
          onClick={fetchCategoryStats}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Object.entries(categoryInfo).map(([categoryKey, info]) => {
        const stats = categoryStats.find(s => s.category === categoryKey);
        const Icon = info.icon;
        
        return (
          <Link
            key={categoryKey}
            href={`/resources?category=${categoryKey.toLowerCase()}`}
            className="group bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
          >
            <div className="flex items-start justify-between mb-4">
              <div className={`p-3 rounded-lg bg-gradient-to-r ${info.gradient} text-white`}>
                <Icon className="h-6 w-6" />
              </div>
              {stats && (
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {stats.averageRating.toFixed(1)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {stats.totalRatings} reviews
                  </p>
                </div>
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {info.title}
            </h3>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              {info.description}
            </p>

            {stats && (
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Resources</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {stats.count}
                  </span>
                </div>

                {/* Skill Level Distribution */}
                <div>
                  <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                    <span>Skill Levels</span>
                  </div>
                  <div className="flex gap-1 h-2">
                    <div 
                      className="bg-green-400 rounded-sm"
                      style={{ 
                        width: `${(stats.skillLevels.BEGINNER / stats.count) * 100}%`,
                        minWidth: stats.skillLevels.BEGINNER > 0 ? '4px' : '0'
                      }}
                      title={`${stats.skillLevels.BEGINNER} Beginner`}
                    ></div>
                    <div 
                      className="bg-yellow-400 rounded-sm"
                      style={{ 
                        width: `${(stats.skillLevels.INTERMEDIATE / stats.count) * 100}%`,
                        minWidth: stats.skillLevels.INTERMEDIATE > 0 ? '4px' : '0'
                      }}
                      title={`${stats.skillLevels.INTERMEDIATE} Intermediate`}
                    ></div>
                    <div 
                      className="bg-red-400 rounded-sm"
                      style={{ 
                        width: `${(stats.skillLevels.ADVANCED / stats.count) * 100}%`,
                        minWidth: stats.skillLevels.ADVANCED > 0 ? '4px' : '0'
                      }}
                      title={`${stats.skillLevels.ADVANCED} Advanced`}
                    ></div>
                  </div>
                </div>

                {/* Resource Types */}
                <div className="flex flex-wrap gap-1">
                  {Object.entries(stats.types).slice(0, 3).map(([type, count]) => {
                    const TypeIcon = getTypeIcon(type);
                    return (
                      <div
                        key={type}
                        className="flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs"
                        title={`${count} ${type}${count !== 1 ? 's' : ''}`}
                      >
                        <TypeIcon className="h-3 w-3" />
                        <span>{count}</span>
                      </div>
                    );
                  })}
                  {Object.keys(stats.types).length > 3 && (
                    <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                      +{Object.keys(stats.types).length - 3}
                    </div>
                  )}
                </div>
              </div>
            )}

            {!stats && (
              <div className="text-center py-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No resources yet
                </p>
              </div>
            )}
          </Link>
        );
      })}
    </div>
  );
}
